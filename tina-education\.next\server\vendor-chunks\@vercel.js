"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@vercel";
exports.ids = ["vendor-chunks/@vercel"];
exports.modules = {

/***/ "(rsc)/./node_modules/@vercel/blob/dist/chunk-Z56QURM6.js":
/*!**********************************************************!*\
  !*** ./node_modules/@vercel/blob/dist/chunk-Z56QURM6.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlobAccessError: () => (/* binding */ BlobAccessError),\n/* harmony export */   BlobClientTokenExpiredError: () => (/* binding */ BlobClientTokenExpiredError),\n/* harmony export */   BlobContentTypeNotAllowedError: () => (/* binding */ BlobContentTypeNotAllowedError),\n/* harmony export */   BlobError: () => (/* binding */ BlobError),\n/* harmony export */   BlobFileTooLargeError: () => (/* binding */ BlobFileTooLargeError),\n/* harmony export */   BlobNotFoundError: () => (/* binding */ BlobNotFoundError),\n/* harmony export */   BlobPathnameMismatchError: () => (/* binding */ BlobPathnameMismatchError),\n/* harmony export */   BlobRequestAbortedError: () => (/* binding */ BlobRequestAbortedError),\n/* harmony export */   BlobServiceNotAvailable: () => (/* binding */ BlobServiceNotAvailable),\n/* harmony export */   BlobServiceRateLimited: () => (/* binding */ BlobServiceRateLimited),\n/* harmony export */   BlobStoreNotFoundError: () => (/* binding */ BlobStoreNotFoundError),\n/* harmony export */   BlobStoreSuspendedError: () => (/* binding */ BlobStoreSuspendedError),\n/* harmony export */   BlobUnknownError: () => (/* binding */ BlobUnknownError),\n/* harmony export */   MAXIMUM_PATHNAME_LENGTH: () => (/* binding */ MAXIMUM_PATHNAME_LENGTH),\n/* harmony export */   createCompleteMultipartUploadMethod: () => (/* binding */ createCompleteMultipartUploadMethod),\n/* harmony export */   createCreateMultipartUploadMethod: () => (/* binding */ createCreateMultipartUploadMethod),\n/* harmony export */   createCreateMultipartUploaderMethod: () => (/* binding */ createCreateMultipartUploaderMethod),\n/* harmony export */   createFolder: () => (/* binding */ createFolder),\n/* harmony export */   createPutMethod: () => (/* binding */ createPutMethod),\n/* harmony export */   createUploadPartMethod: () => (/* binding */ createUploadPartMethod),\n/* harmony export */   disallowedPathnameCharacters: () => (/* binding */ disallowedPathnameCharacters),\n/* harmony export */   getDownloadUrl: () => (/* binding */ getDownloadUrl),\n/* harmony export */   getTokenFromOptionsOrEnv: () => (/* binding */ getTokenFromOptionsOrEnv),\n/* harmony export */   requestApi: () => (/* binding */ requestApi)\n/* harmony export */ });\n/* harmony import */ var is_node_process__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! is-node-process */ \"(rsc)/./node_modules/is-node-process/lib/index.mjs\");\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! stream */ \"stream\");\n/* harmony import */ var is_buffer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! is-buffer */ \"(rsc)/./node_modules/is-buffer/index.js\");\n/* harmony import */ var async_retry__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! async-retry */ \"(rsc)/./node_modules/async-retry/lib/index.js\");\n/* harmony import */ var undici__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! undici */ \"(rsc)/./node_modules/undici/index.js\");\n/* harmony import */ var throttleit__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! throttleit */ \"(rsc)/./node_modules/throttleit/index.js\");\n// src/helpers.ts\n\n\n// src/multipart/helpers.ts\n\n\nvar supportsNewBlobFromArrayBuffer = new Promise((resolve) => {\n  try {\n    const helloAsArrayBuffer = new Uint8Array([104, 101, 108, 108, 111]);\n    const blob = new Blob([helloAsArrayBuffer]);\n    blob.text().then((text) => {\n      resolve(text === \"hello\");\n    }).catch(() => {\n      resolve(false);\n    });\n  } catch {\n    resolve(false);\n  }\n});\nasync function toReadableStream(value) {\n  if (value instanceof ReadableStream) {\n    return value;\n  }\n  if (value instanceof Blob) {\n    return value.stream();\n  }\n  if (isNodeJsReadableStream(value)) {\n    return stream__WEBPACK_IMPORTED_MODULE_1__.Readable.toWeb(value);\n  }\n  let streamValue;\n  if (value instanceof ArrayBuffer) {\n    streamValue = new Uint8Array(value);\n  } else if (isNodeJsBuffer(value)) {\n    streamValue = value;\n  } else {\n    streamValue = stringToUint8Array(value);\n  }\n  if (await supportsNewBlobFromArrayBuffer) {\n    return new Blob([streamValue]).stream();\n  }\n  return new ReadableStream({\n    start(controller) {\n      controller.enqueue(streamValue);\n      controller.close();\n    }\n  });\n}\nfunction isNodeJsReadableStream(value) {\n  return typeof value === \"object\" && typeof value.pipe === \"function\" && value.readable && typeof value._read === \"function\" && // @ts-expect-error _readableState does exists on Readable\n  typeof value._readableState === \"object\";\n}\nfunction stringToUint8Array(s) {\n  const enc = new TextEncoder();\n  return enc.encode(s);\n}\nfunction isNodeJsBuffer(value) {\n  return is_buffer__WEBPACK_IMPORTED_MODULE_2__(value);\n}\n\n// src/bytes.ts\nvar parseRegExp = /^((-|\\+)?(\\d+(?:\\.\\d+)?)) *(kb|mb|gb|tb|pb)$/i;\nvar map = {\n  b: 1,\n  // eslint-disable-next-line no-bitwise -- fine\n  kb: 1 << 10,\n  // eslint-disable-next-line no-bitwise -- fine\n  mb: 1 << 20,\n  // eslint-disable-next-line no-bitwise -- fine\n  gb: 1 << 30,\n  tb: Math.pow(1024, 4),\n  pb: Math.pow(1024, 5)\n};\nfunction bytes(val) {\n  if (typeof val === \"number\" && !isNaN(val)) {\n    return val;\n  }\n  if (typeof val !== \"string\") {\n    return null;\n  }\n  const results = parseRegExp.exec(val);\n  let floatValue;\n  let unit = \"b\";\n  if (!results) {\n    floatValue = parseInt(val, 10);\n  } else {\n    const [, res, , , unitMatch] = results;\n    if (!res) {\n      return null;\n    }\n    floatValue = parseFloat(res);\n    if (unitMatch) {\n      unit = unitMatch.toLowerCase();\n    }\n  }\n  if (isNaN(floatValue)) {\n    return null;\n  }\n  return Math.floor(map[unit] * floatValue);\n}\n\n// src/helpers.ts\nvar defaultVercelBlobApiUrl = \"https://vercel.com/api/blob\";\nfunction getTokenFromOptionsOrEnv(options) {\n  if (options == null ? void 0 : options.token) {\n    return options.token;\n  }\n  if (process.env.BLOB_READ_WRITE_TOKEN) {\n    return process.env.BLOB_READ_WRITE_TOKEN;\n  }\n  throw new BlobError(\n    \"No token found. Either configure the `BLOB_READ_WRITE_TOKEN` environment variable, or pass a `token` option to your calls.\"\n  );\n}\nvar BlobError = class extends Error {\n  constructor(message) {\n    super(`Vercel Blob: ${message}`);\n  }\n};\nfunction getDownloadUrl(blobUrl) {\n  const url = new URL(blobUrl);\n  url.searchParams.set(\"download\", \"1\");\n  return url.toString();\n}\nfunction isPlainObject(value) {\n  if (typeof value !== \"object\" || value === null) {\n    return false;\n  }\n  const prototype = Object.getPrototypeOf(value);\n  return (prototype === null || prototype === Object.prototype || Object.getPrototypeOf(prototype) === null) && !(Symbol.toStringTag in value) && !(Symbol.iterator in value);\n}\nvar disallowedPathnameCharacters = [\"//\"];\nvar supportsRequestStreams = (() => {\n  if ((0,is_node_process__WEBPACK_IMPORTED_MODULE_0__.isNodeProcess)()) {\n    return true;\n  }\n  const apiUrl = getApiUrl();\n  if (apiUrl.startsWith(\"http://localhost\")) {\n    return false;\n  }\n  let duplexAccessed = false;\n  const hasContentType = new Request(getApiUrl(), {\n    body: new ReadableStream(),\n    method: \"POST\",\n    // @ts-expect-error -- TypeScript doesn't yet have duplex but it's in the spec: https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1729\n    get duplex() {\n      duplexAccessed = true;\n      return \"half\";\n    }\n  }).headers.has(\"Content-Type\");\n  return duplexAccessed && !hasContentType;\n})();\nfunction getApiUrl(pathname = \"\") {\n  let baseUrl = null;\n  try {\n    baseUrl = process.env.VERCEL_BLOB_API_URL || process.env.NEXT_PUBLIC_VERCEL_BLOB_API_URL;\n  } catch {\n  }\n  return `${baseUrl || defaultVercelBlobApiUrl}${pathname}`;\n}\nvar TEXT_ENCODER = typeof TextEncoder === \"function\" ? new TextEncoder() : null;\nfunction computeBodyLength(body) {\n  if (!body) {\n    return 0;\n  }\n  if (typeof body === \"string\") {\n    if (TEXT_ENCODER) {\n      return TEXT_ENCODER.encode(body).byteLength;\n    }\n    return new Blob([body]).size;\n  }\n  if (\"byteLength\" in body && typeof body.byteLength === \"number\") {\n    return body.byteLength;\n  }\n  if (\"size\" in body && typeof body.size === \"number\") {\n    return body.size;\n  }\n  return 0;\n}\nvar createChunkTransformStream = (chunkSize, onProgress) => {\n  let buffer = new Uint8Array(0);\n  return new TransformStream({\n    transform(chunk, controller) {\n      queueMicrotask(() => {\n        const newBuffer = new Uint8Array(buffer.length + chunk.byteLength);\n        newBuffer.set(buffer);\n        newBuffer.set(new Uint8Array(chunk), buffer.length);\n        buffer = newBuffer;\n        while (buffer.length >= chunkSize) {\n          const newChunk = buffer.slice(0, chunkSize);\n          controller.enqueue(newChunk);\n          onProgress == null ? void 0 : onProgress(newChunk.byteLength);\n          buffer = buffer.slice(chunkSize);\n        }\n      });\n    },\n    flush(controller) {\n      queueMicrotask(() => {\n        if (buffer.length > 0) {\n          controller.enqueue(buffer);\n          onProgress == null ? void 0 : onProgress(buffer.byteLength);\n        }\n      });\n    }\n  });\n};\nfunction isReadableStream(value) {\n  return (\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition -- Not present in Node.js 16\n    globalThis.ReadableStream && // TODO: Can be removed once Node.js 16 is no more required internally\n    value instanceof ReadableStream\n  );\n}\nfunction isStream(value) {\n  if (isReadableStream(value)) {\n    return true;\n  }\n  if (isNodeJsReadableStream(value)) {\n    return true;\n  }\n  return false;\n}\n\n// src/api.ts\n\n\n// src/is-network-error.ts\nvar objectToString = Object.prototype.toString;\nvar isError = (value) => objectToString.call(value) === \"[object Error]\";\nvar errorMessages = /* @__PURE__ */ new Set([\n  \"network error\",\n  // Chrome\n  \"Failed to fetch\",\n  // Chrome\n  \"NetworkError when attempting to fetch resource.\",\n  // Firefox\n  \"The Internet connection appears to be offline.\",\n  // Safari 16\n  \"Load failed\",\n  // Safari 17+\n  \"Network request failed\",\n  // `cross-fetch`\n  \"fetch failed\",\n  // Undici (Node.js)\n  \"terminated\"\n  // Undici (Node.js)\n]);\nfunction isNetworkError(error) {\n  const isValid = error && isError(error) && error.name === \"TypeError\" && typeof error.message === \"string\";\n  if (!isValid) {\n    return false;\n  }\n  if (error.message === \"Load failed\") {\n    return error.stack === void 0;\n  }\n  return errorMessages.has(error.message);\n}\n\n// src/debug.ts\nvar debugIsActive = false;\nvar _a, _b;\ntry {\n  if (((_a = process.env.DEBUG) == null ? void 0 : _a.includes(\"blob\")) || ((_b = process.env.NEXT_PUBLIC_DEBUG) == null ? void 0 : _b.includes(\"blob\"))) {\n    debugIsActive = true;\n  }\n} catch (error) {\n}\nfunction debug(message, ...args) {\n  if (debugIsActive) {\n    console.debug(`vercel-blob: ${message}`, ...args);\n  }\n}\n\n// src/fetch.ts\n\nvar hasFetch = typeof undici__WEBPACK_IMPORTED_MODULE_4__.fetch === \"function\";\nvar hasFetchWithUploadProgress = hasFetch && supportsRequestStreams;\nvar CHUNK_SIZE = 64 * 1024;\nvar blobFetch = async ({\n  input,\n  init,\n  onUploadProgress\n}) => {\n  debug(\"using fetch\");\n  let body;\n  if (init.body) {\n    if (onUploadProgress) {\n      const stream = await toReadableStream(init.body);\n      let loaded = 0;\n      const chunkTransformStream = createChunkTransformStream(\n        CHUNK_SIZE,\n        (newLoaded) => {\n          loaded += newLoaded;\n          onUploadProgress(loaded);\n        }\n      );\n      body = stream.pipeThrough(chunkTransformStream);\n    } else {\n      body = init.body;\n    }\n  }\n  const duplex = supportsRequestStreams && body && isStream(body) ? \"half\" : void 0;\n  return (0,undici__WEBPACK_IMPORTED_MODULE_4__.fetch)(\n    input,\n    // @ts-expect-error -- Blob and Nodejs Blob are triggering type errors, fine with it\n    {\n      ...init,\n      ...init.body ? { body } : {},\n      duplex\n    }\n  );\n};\n\n// src/xhr.ts\nvar hasXhr = typeof XMLHttpRequest !== \"undefined\";\nvar blobXhr = async ({\n  input,\n  init,\n  onUploadProgress\n}) => {\n  debug(\"using xhr\");\n  let body = null;\n  if (init.body) {\n    if (isReadableStream(init.body)) {\n      body = await new Response(init.body).blob();\n    } else {\n      body = init.body;\n    }\n  }\n  return new Promise((resolve, reject) => {\n    const xhr = new XMLHttpRequest();\n    xhr.open(init.method || \"GET\", input.toString(), true);\n    if (onUploadProgress) {\n      xhr.upload.addEventListener(\"progress\", (event) => {\n        if (event.lengthComputable) {\n          onUploadProgress(event.loaded);\n        }\n      });\n    }\n    xhr.onload = () => {\n      var _a3;\n      if ((_a3 = init.signal) == null ? void 0 : _a3.aborted) {\n        reject(new DOMException(\"The user aborted the request.\", \"AbortError\"));\n        return;\n      }\n      const headers = new Headers();\n      const rawHeaders = xhr.getAllResponseHeaders().trim().split(/[\\r\\n]+/);\n      rawHeaders.forEach((line) => {\n        const parts = line.split(\": \");\n        const key = parts.shift();\n        const value = parts.join(\": \");\n        if (key) headers.set(key.toLowerCase(), value);\n      });\n      const response = new Response(xhr.response, {\n        status: xhr.status,\n        statusText: xhr.statusText,\n        headers\n      });\n      resolve(response);\n    };\n    xhr.onerror = () => {\n      reject(new TypeError(\"Network request failed\"));\n    };\n    xhr.ontimeout = () => {\n      reject(new TypeError(\"Network request timed out\"));\n    };\n    xhr.onabort = () => {\n      reject(new DOMException(\"The user aborted a request.\", \"AbortError\"));\n    };\n    if (init.headers) {\n      const headers = new Headers(init.headers);\n      headers.forEach((value, key) => {\n        xhr.setRequestHeader(key, value);\n      });\n    }\n    if (init.signal) {\n      init.signal.addEventListener(\"abort\", () => {\n        xhr.abort();\n      });\n      if (init.signal.aborted) {\n        xhr.abort();\n        return;\n      }\n    }\n    xhr.send(body);\n  });\n};\n\n// src/request.ts\nvar blobRequest = async ({\n  input,\n  init,\n  onUploadProgress\n}) => {\n  if (onUploadProgress) {\n    if (hasFetchWithUploadProgress) {\n      return blobFetch({ input, init, onUploadProgress });\n    }\n    if (hasXhr) {\n      return blobXhr({ input, init, onUploadProgress });\n    }\n  }\n  if (hasFetch) {\n    return blobFetch({ input, init });\n  }\n  if (hasXhr) {\n    return blobXhr({ input, init });\n  }\n  throw new Error(\"No request implementation available\");\n};\n\n// src/dom-exception.ts\nvar _a2;\nvar DOMException2 = (_a2 = globalThis.DOMException) != null ? _a2 : (() => {\n  try {\n    atob(\"~\");\n  } catch (err) {\n    return Object.getPrototypeOf(err).constructor;\n  }\n})();\n\n// src/api.ts\nvar MAXIMUM_PATHNAME_LENGTH = 950;\nvar BlobAccessError = class extends BlobError {\n  constructor() {\n    super(\"Access denied, please provide a valid token for this resource.\");\n  }\n};\nvar BlobContentTypeNotAllowedError = class extends BlobError {\n  constructor(message) {\n    super(`Content type mismatch, ${message}.`);\n  }\n};\nvar BlobPathnameMismatchError = class extends BlobError {\n  constructor(message) {\n    super(\n      `Pathname mismatch, ${message}. Check the pathname used in upload() or put() matches the one from the client token.`\n    );\n  }\n};\nvar BlobClientTokenExpiredError = class extends BlobError {\n  constructor() {\n    super(\"Client token has expired.\");\n  }\n};\nvar BlobFileTooLargeError = class extends BlobError {\n  constructor(message) {\n    super(`File is too large, ${message}.`);\n  }\n};\nvar BlobStoreNotFoundError = class extends BlobError {\n  constructor() {\n    super(\"This store does not exist.\");\n  }\n};\nvar BlobStoreSuspendedError = class extends BlobError {\n  constructor() {\n    super(\"This store has been suspended.\");\n  }\n};\nvar BlobUnknownError = class extends BlobError {\n  constructor() {\n    super(\"Unknown error, please visit https://vercel.com/help.\");\n  }\n};\nvar BlobNotFoundError = class extends BlobError {\n  constructor() {\n    super(\"The requested blob does not exist\");\n  }\n};\nvar BlobServiceNotAvailable = class extends BlobError {\n  constructor() {\n    super(\"The blob service is currently not available. Please try again.\");\n  }\n};\nvar BlobServiceRateLimited = class extends BlobError {\n  constructor(seconds) {\n    super(\n      `Too many requests please lower the number of concurrent requests ${seconds ? ` - try again in ${seconds} seconds` : \"\"}.`\n    );\n    this.retryAfter = seconds != null ? seconds : 0;\n  }\n};\nvar BlobRequestAbortedError = class extends BlobError {\n  constructor() {\n    super(\"The request was aborted.\");\n  }\n};\nvar BLOB_API_VERSION = 11;\nfunction getApiVersion() {\n  let versionOverride = null;\n  try {\n    versionOverride = process.env.VERCEL_BLOB_API_VERSION_OVERRIDE || process.env.NEXT_PUBLIC_VERCEL_BLOB_API_VERSION_OVERRIDE;\n  } catch {\n  }\n  return `${versionOverride != null ? versionOverride : BLOB_API_VERSION}`;\n}\nfunction getRetries() {\n  try {\n    const retries = process.env.VERCEL_BLOB_RETRIES || \"10\";\n    return parseInt(retries, 10);\n  } catch {\n    return 10;\n  }\n}\nfunction createBlobServiceRateLimited(response) {\n  const retryAfter = response.headers.get(\"retry-after\");\n  return new BlobServiceRateLimited(\n    retryAfter ? parseInt(retryAfter, 10) : void 0\n  );\n}\nasync function getBlobError(response) {\n  var _a3, _b2, _c;\n  let code;\n  let message;\n  try {\n    const data = await response.json();\n    code = (_b2 = (_a3 = data.error) == null ? void 0 : _a3.code) != null ? _b2 : \"unknown_error\";\n    message = (_c = data.error) == null ? void 0 : _c.message;\n  } catch {\n    code = \"unknown_error\";\n  }\n  if ((message == null ? void 0 : message.includes(\"contentType\")) && message.includes(\"is not allowed\")) {\n    code = \"content_type_not_allowed\";\n  }\n  if ((message == null ? void 0 : message.includes('\"pathname\"')) && message.includes(\"does not match the token payload\")) {\n    code = \"client_token_pathname_mismatch\";\n  }\n  if (message === \"Token expired\") {\n    code = \"client_token_expired\";\n  }\n  if (message == null ? void 0 : message.includes(\"the file length cannot be greater than\")) {\n    code = \"file_too_large\";\n  }\n  let error;\n  switch (code) {\n    case \"store_suspended\":\n      error = new BlobStoreSuspendedError();\n      break;\n    case \"forbidden\":\n      error = new BlobAccessError();\n      break;\n    case \"content_type_not_allowed\":\n      error = new BlobContentTypeNotAllowedError(message);\n      break;\n    case \"client_token_pathname_mismatch\":\n      error = new BlobPathnameMismatchError(message);\n      break;\n    case \"client_token_expired\":\n      error = new BlobClientTokenExpiredError();\n      break;\n    case \"file_too_large\":\n      error = new BlobFileTooLargeError(message);\n      break;\n    case \"not_found\":\n      error = new BlobNotFoundError();\n      break;\n    case \"store_not_found\":\n      error = new BlobStoreNotFoundError();\n      break;\n    case \"bad_request\":\n      error = new BlobError(message != null ? message : \"Bad request\");\n      break;\n    case \"service_unavailable\":\n      error = new BlobServiceNotAvailable();\n      break;\n    case \"rate_limited\":\n      error = createBlobServiceRateLimited(response);\n      break;\n    case \"unknown_error\":\n    case \"not_allowed\":\n    default:\n      error = new BlobUnknownError();\n      break;\n  }\n  return { code, error };\n}\nasync function requestApi(pathname, init, commandOptions) {\n  const apiVersion = getApiVersion();\n  const token = getTokenFromOptionsOrEnv(commandOptions);\n  const extraHeaders = getProxyThroughAlternativeApiHeaderFromEnv();\n  const [, , , storeId = \"\"] = token.split(\"_\");\n  const requestId = `${storeId}:${Date.now()}:${Math.random().toString(16).slice(2)}`;\n  let retryCount = 0;\n  let bodyLength = 0;\n  let totalLoaded = 0;\n  const sendBodyLength = (commandOptions == null ? void 0 : commandOptions.onUploadProgress) || shouldUseXContentLength();\n  if (init.body && // 1. For upload progress we always need to know the total size of the body\n  // 2. In development we need the header for put() to work correctly when passing a stream\n  sendBodyLength) {\n    bodyLength = computeBodyLength(init.body);\n  }\n  if (commandOptions == null ? void 0 : commandOptions.onUploadProgress) {\n    commandOptions.onUploadProgress({\n      loaded: 0,\n      total: bodyLength,\n      percentage: 0\n    });\n  }\n  const apiResponse = await async_retry__WEBPACK_IMPORTED_MODULE_3__(\n    async (bail) => {\n      let res;\n      try {\n        res = await blobRequest({\n          input: getApiUrl(pathname),\n          init: {\n            ...init,\n            headers: {\n              \"x-api-blob-request-id\": requestId,\n              \"x-api-blob-request-attempt\": String(retryCount),\n              \"x-api-version\": apiVersion,\n              ...sendBodyLength ? { \"x-content-length\": String(bodyLength) } : {},\n              authorization: `Bearer ${token}`,\n              ...extraHeaders,\n              ...init.headers\n            }\n          },\n          onUploadProgress: (commandOptions == null ? void 0 : commandOptions.onUploadProgress) ? (loaded) => {\n            var _a3;\n            const total = bodyLength !== 0 ? bodyLength : loaded;\n            totalLoaded = loaded;\n            const percentage = bodyLength > 0 ? Number((loaded / total * 100).toFixed(2)) : 0;\n            if (percentage === 100 && bodyLength > 0) {\n              return;\n            }\n            (_a3 = commandOptions.onUploadProgress) == null ? void 0 : _a3.call(commandOptions, {\n              loaded,\n              // When passing a stream to put(), we have no way to know the total size of the body.\n              // Instead of defining total as total?: number we decided to set the total to the currently\n              // loaded number. This is not inaccurate and way more practical for DX.\n              // Passing down a stream to put() is very rare\n              total,\n              percentage\n            });\n          } : void 0\n        });\n      } catch (error2) {\n        if (error2 instanceof DOMException2 && error2.name === \"AbortError\") {\n          bail(new BlobRequestAbortedError());\n          return;\n        }\n        if (isNetworkError(error2)) {\n          throw error2;\n        }\n        if (error2 instanceof TypeError) {\n          bail(error2);\n          return;\n        }\n        throw error2;\n      }\n      if (res.ok) {\n        return res;\n      }\n      const { code, error } = await getBlobError(res);\n      if (code === \"unknown_error\" || code === \"service_unavailable\" || code === \"internal_server_error\") {\n        throw error;\n      }\n      bail(error);\n    },\n    {\n      retries: getRetries(),\n      onRetry: (error) => {\n        if (error instanceof Error) {\n          debug(`retrying API request to ${pathname}`, error.message);\n        }\n        retryCount = retryCount + 1;\n      }\n    }\n  );\n  if (!apiResponse) {\n    throw new BlobUnknownError();\n  }\n  if (commandOptions == null ? void 0 : commandOptions.onUploadProgress) {\n    commandOptions.onUploadProgress({\n      loaded: totalLoaded,\n      total: totalLoaded,\n      percentage: 100\n    });\n  }\n  return await apiResponse.json();\n}\nfunction getProxyThroughAlternativeApiHeaderFromEnv() {\n  const extraHeaders = {};\n  try {\n    if (\"VERCEL_BLOB_PROXY_THROUGH_ALTERNATIVE_API\" in process.env && process.env.VERCEL_BLOB_PROXY_THROUGH_ALTERNATIVE_API !== void 0) {\n      extraHeaders[\"x-proxy-through-alternative-api\"] = process.env.VERCEL_BLOB_PROXY_THROUGH_ALTERNATIVE_API;\n    } else if (\"NEXT_PUBLIC_VERCEL_BLOB_PROXY_THROUGH_ALTERNATIVE_API\" in process.env && process.env.NEXT_PUBLIC_VERCEL_BLOB_PROXY_THROUGH_ALTERNATIVE_API !== void 0) {\n      extraHeaders[\"x-proxy-through-alternative-api\"] = process.env.NEXT_PUBLIC_VERCEL_BLOB_PROXY_THROUGH_ALTERNATIVE_API;\n    }\n  } catch {\n  }\n  return extraHeaders;\n}\nfunction shouldUseXContentLength() {\n  try {\n    return process.env.VERCEL_BLOB_USE_X_CONTENT_LENGTH === \"1\";\n  } catch {\n    return false;\n  }\n}\n\n// src/put-helpers.ts\nvar putOptionHeaderMap = {\n  cacheControlMaxAge: \"x-cache-control-max-age\",\n  addRandomSuffix: \"x-add-random-suffix\",\n  allowOverwrite: \"x-allow-overwrite\",\n  contentType: \"x-content-type\"\n};\nfunction createPutHeaders(allowedOptions, options) {\n  const headers = {};\n  if (allowedOptions.includes(\"contentType\") && options.contentType) {\n    headers[putOptionHeaderMap.contentType] = options.contentType;\n  }\n  if (allowedOptions.includes(\"addRandomSuffix\") && options.addRandomSuffix !== void 0) {\n    headers[putOptionHeaderMap.addRandomSuffix] = options.addRandomSuffix ? \"1\" : \"0\";\n  }\n  if (allowedOptions.includes(\"allowOverwrite\") && options.allowOverwrite !== void 0) {\n    headers[putOptionHeaderMap.allowOverwrite] = options.allowOverwrite ? \"1\" : \"0\";\n  }\n  if (allowedOptions.includes(\"cacheControlMaxAge\") && options.cacheControlMaxAge !== void 0) {\n    headers[putOptionHeaderMap.cacheControlMaxAge] = options.cacheControlMaxAge.toString();\n  }\n  return headers;\n}\nasync function createPutOptions({\n  pathname,\n  options,\n  extraChecks,\n  getToken\n}) {\n  if (!pathname) {\n    throw new BlobError(\"pathname is required\");\n  }\n  if (pathname.length > MAXIMUM_PATHNAME_LENGTH) {\n    throw new BlobError(\n      `pathname is too long, maximum length is ${MAXIMUM_PATHNAME_LENGTH}`\n    );\n  }\n  for (const invalidCharacter of disallowedPathnameCharacters) {\n    if (pathname.includes(invalidCharacter)) {\n      throw new BlobError(\n        `pathname cannot contain \"${invalidCharacter}\", please encode it if needed`\n      );\n    }\n  }\n  if (!options) {\n    throw new BlobError(\"missing options, see usage\");\n  }\n  if (options.access !== \"public\") {\n    throw new BlobError('access must be \"public\"');\n  }\n  if (extraChecks) {\n    extraChecks(options);\n  }\n  if (getToken) {\n    options.token = await getToken(pathname, options);\n  }\n  return options;\n}\n\n// src/multipart/complete.ts\nfunction createCompleteMultipartUploadMethod({ allowedOptions, getToken, extraChecks }) {\n  return async (pathname, parts, optionsInput) => {\n    const options = await createPutOptions({\n      pathname,\n      options: optionsInput,\n      extraChecks,\n      getToken\n    });\n    const headers = createPutHeaders(allowedOptions, options);\n    return completeMultipartUpload({\n      uploadId: options.uploadId,\n      key: options.key,\n      pathname,\n      headers,\n      options,\n      parts\n    });\n  };\n}\nasync function completeMultipartUpload({\n  uploadId,\n  key,\n  pathname,\n  parts,\n  headers,\n  options\n}) {\n  const params = new URLSearchParams({ pathname });\n  try {\n    const response = await requestApi(\n      `/mpu?${params.toString()}`,\n      {\n        method: \"POST\",\n        headers: {\n          ...headers,\n          \"content-type\": \"application/json\",\n          \"x-mpu-action\": \"complete\",\n          \"x-mpu-upload-id\": uploadId,\n          // key can be any utf8 character so we need to encode it as HTTP headers can only be us-ascii\n          // https://www.rfc-editor.org/rfc/rfc7230#swection-3.2.4\n          \"x-mpu-key\": encodeURIComponent(key)\n        },\n        body: JSON.stringify(parts),\n        signal: options.abortSignal\n      },\n      options\n    );\n    debug(\"mpu: complete\", response);\n    return response;\n  } catch (error) {\n    if (error instanceof TypeError && (error.message === \"Failed to fetch\" || error.message === \"fetch failed\")) {\n      throw new BlobServiceNotAvailable();\n    } else {\n      throw error;\n    }\n  }\n}\n\n// src/multipart/create.ts\nfunction createCreateMultipartUploadMethod({ allowedOptions, getToken, extraChecks }) {\n  return async (pathname, optionsInput) => {\n    const options = await createPutOptions({\n      pathname,\n      options: optionsInput,\n      extraChecks,\n      getToken\n    });\n    const headers = createPutHeaders(allowedOptions, options);\n    const createMultipartUploadResponse = await createMultipartUpload(\n      pathname,\n      headers,\n      options\n    );\n    return {\n      key: createMultipartUploadResponse.key,\n      uploadId: createMultipartUploadResponse.uploadId\n    };\n  };\n}\nasync function createMultipartUpload(pathname, headers, options) {\n  debug(\"mpu: create\", \"pathname:\", pathname);\n  const params = new URLSearchParams({ pathname });\n  try {\n    const response = await requestApi(\n      `/mpu?${params.toString()}`,\n      {\n        method: \"POST\",\n        headers: {\n          ...headers,\n          \"x-mpu-action\": \"create\"\n        },\n        signal: options.abortSignal\n      },\n      options\n    );\n    debug(\"mpu: create\", response);\n    return response;\n  } catch (error) {\n    if (error instanceof TypeError && (error.message === \"Failed to fetch\" || error.message === \"fetch failed\")) {\n      throw new BlobServiceNotAvailable();\n    }\n    throw error;\n  }\n}\n\n// src/multipart/upload.ts\n\nfunction createUploadPartMethod({ allowedOptions, getToken, extraChecks }) {\n  return async (pathname, body, optionsInput) => {\n    const options = await createPutOptions({\n      pathname,\n      options: optionsInput,\n      extraChecks,\n      getToken\n    });\n    const headers = createPutHeaders(allowedOptions, options);\n    if (isPlainObject(body)) {\n      throw new BlobError(\n        \"Body must be a string, buffer or stream. You sent a plain JavaScript object, double check what you're trying to upload.\"\n      );\n    }\n    const result = await uploadPart({\n      uploadId: options.uploadId,\n      key: options.key,\n      pathname,\n      part: { blob: body, partNumber: options.partNumber },\n      headers,\n      options\n    });\n    return {\n      etag: result.etag,\n      partNumber: options.partNumber\n    };\n  };\n}\nasync function uploadPart({\n  uploadId,\n  key,\n  pathname,\n  headers,\n  options,\n  internalAbortController = new AbortController(),\n  part\n}) {\n  var _a3, _b2, _c;\n  const params = new URLSearchParams({ pathname });\n  const responsePromise = requestApi(\n    `/mpu?${params.toString()}`,\n    {\n      signal: internalAbortController.signal,\n      method: \"POST\",\n      headers: {\n        ...headers,\n        \"x-mpu-action\": \"upload\",\n        \"x-mpu-key\": encodeURIComponent(key),\n        \"x-mpu-upload-id\": uploadId,\n        \"x-mpu-part-number\": part.partNumber.toString()\n      },\n      // weird things between undici types and native fetch types\n      body: part.blob\n    },\n    options\n  );\n  function handleAbort() {\n    internalAbortController.abort();\n  }\n  if ((_a3 = options.abortSignal) == null ? void 0 : _a3.aborted) {\n    handleAbort();\n  } else {\n    (_b2 = options.abortSignal) == null ? void 0 : _b2.addEventListener(\"abort\", handleAbort);\n  }\n  const response = await responsePromise;\n  (_c = options.abortSignal) == null ? void 0 : _c.removeEventListener(\"abort\", handleAbort);\n  return response;\n}\nvar maxConcurrentUploads = typeof window !== \"undefined\" ? 6 : 8;\nvar partSizeInBytes = 8 * 1024 * 1024;\nvar maxBytesInMemory = maxConcurrentUploads * partSizeInBytes * 2;\nfunction uploadAllParts({\n  uploadId,\n  key,\n  pathname,\n  stream,\n  headers,\n  options,\n  totalToLoad\n}) {\n  debug(\"mpu: upload init\", \"key:\", key);\n  const internalAbortController = new AbortController();\n  return new Promise((resolve, reject) => {\n    const partsToUpload = [];\n    const completedParts = [];\n    const reader = stream.getReader();\n    let activeUploads = 0;\n    let reading = false;\n    let currentPartNumber = 1;\n    let rejected = false;\n    let currentBytesInMemory = 0;\n    let doneReading = false;\n    let bytesSent = 0;\n    let arrayBuffers = [];\n    let currentPartBytesRead = 0;\n    let onUploadProgress;\n    const totalLoadedPerPartNumber = {};\n    if (options.onUploadProgress) {\n      onUploadProgress = throttleit__WEBPACK_IMPORTED_MODULE_5__(() => {\n        var _a3;\n        const loaded = Object.values(totalLoadedPerPartNumber).reduce(\n          (acc, cur) => {\n            return acc + cur;\n          },\n          0\n        );\n        const total = totalToLoad || loaded;\n        const percentage = totalToLoad > 0 ? Number(((loaded / totalToLoad || loaded) * 100).toFixed(2)) : 0;\n        (_a3 = options.onUploadProgress) == null ? void 0 : _a3.call(options, { loaded, total, percentage });\n      }, 150);\n    }\n    read().catch(cancel);\n    async function read() {\n      debug(\n        \"mpu: upload read start\",\n        \"activeUploads:\",\n        activeUploads,\n        \"currentBytesInMemory:\",\n        `${bytes(currentBytesInMemory)}/${bytes(maxBytesInMemory)}`,\n        \"bytesSent:\",\n        bytes(bytesSent)\n      );\n      reading = true;\n      while (currentBytesInMemory < maxBytesInMemory && !rejected) {\n        try {\n          const { value, done } = await reader.read();\n          if (done) {\n            doneReading = true;\n            debug(\"mpu: upload read consumed the whole stream\");\n            if (arrayBuffers.length > 0) {\n              partsToUpload.push({\n                partNumber: currentPartNumber++,\n                blob: new Blob(arrayBuffers, {\n                  type: \"application/octet-stream\"\n                })\n              });\n              sendParts();\n            }\n            reading = false;\n            return;\n          }\n          currentBytesInMemory += value.byteLength;\n          let valueOffset = 0;\n          while (valueOffset < value.byteLength) {\n            const remainingPartSize = partSizeInBytes - currentPartBytesRead;\n            const endOffset = Math.min(\n              valueOffset + remainingPartSize,\n              value.byteLength\n            );\n            const chunk = value.slice(valueOffset, endOffset);\n            arrayBuffers.push(chunk);\n            currentPartBytesRead += chunk.byteLength;\n            valueOffset = endOffset;\n            if (currentPartBytesRead === partSizeInBytes) {\n              partsToUpload.push({\n                partNumber: currentPartNumber++,\n                blob: new Blob(arrayBuffers, {\n                  type: \"application/octet-stream\"\n                })\n              });\n              arrayBuffers = [];\n              currentPartBytesRead = 0;\n              sendParts();\n            }\n          }\n        } catch (error) {\n          cancel(error);\n        }\n      }\n      debug(\n        \"mpu: upload read end\",\n        \"activeUploads:\",\n        activeUploads,\n        \"currentBytesInMemory:\",\n        `${bytes(currentBytesInMemory)}/${bytes(maxBytesInMemory)}`,\n        \"bytesSent:\",\n        bytes(bytesSent)\n      );\n      reading = false;\n    }\n    async function sendPart(part) {\n      activeUploads++;\n      debug(\n        \"mpu: upload send part start\",\n        \"partNumber:\",\n        part.partNumber,\n        \"size:\",\n        part.blob.size,\n        \"activeUploads:\",\n        activeUploads,\n        \"currentBytesInMemory:\",\n        `${bytes(currentBytesInMemory)}/${bytes(maxBytesInMemory)}`,\n        \"bytesSent:\",\n        bytes(bytesSent)\n      );\n      try {\n        const uploadProgressForPart = options.onUploadProgress ? (event) => {\n          totalLoadedPerPartNumber[part.partNumber] = event.loaded;\n          if (onUploadProgress) {\n            onUploadProgress();\n          }\n        } : void 0;\n        const completedPart = await uploadPart({\n          uploadId,\n          key,\n          pathname,\n          headers,\n          options: {\n            ...options,\n            onUploadProgress: uploadProgressForPart\n          },\n          internalAbortController,\n          part\n        });\n        debug(\n          \"mpu: upload send part end\",\n          \"partNumber:\",\n          part.partNumber,\n          \"activeUploads\",\n          activeUploads,\n          \"currentBytesInMemory:\",\n          `${bytes(currentBytesInMemory)}/${bytes(maxBytesInMemory)}`,\n          \"bytesSent:\",\n          bytes(bytesSent)\n        );\n        if (rejected) {\n          return;\n        }\n        completedParts.push({\n          partNumber: part.partNumber,\n          etag: completedPart.etag\n        });\n        currentBytesInMemory -= part.blob.size;\n        activeUploads--;\n        bytesSent += part.blob.size;\n        if (partsToUpload.length > 0) {\n          sendParts();\n        }\n        if (doneReading) {\n          if (activeUploads === 0) {\n            reader.releaseLock();\n            resolve(completedParts);\n          }\n          return;\n        }\n        if (!reading) {\n          read().catch(cancel);\n        }\n      } catch (error) {\n        cancel(error);\n      }\n    }\n    function sendParts() {\n      if (rejected) {\n        return;\n      }\n      debug(\n        \"send parts\",\n        \"activeUploads\",\n        activeUploads,\n        \"partsToUpload\",\n        partsToUpload.length\n      );\n      while (activeUploads < maxConcurrentUploads && partsToUpload.length > 0) {\n        const partToSend = partsToUpload.shift();\n        if (partToSend) {\n          void sendPart(partToSend);\n        }\n      }\n    }\n    function cancel(error) {\n      if (rejected) {\n        return;\n      }\n      rejected = true;\n      internalAbortController.abort();\n      reader.releaseLock();\n      if (error instanceof TypeError && (error.message === \"Failed to fetch\" || error.message === \"fetch failed\")) {\n        reject(new BlobServiceNotAvailable());\n      } else {\n        reject(error);\n      }\n    }\n  });\n}\n\n// src/put.ts\n\n\n// src/multipart/uncontrolled.ts\nasync function uncontrolledMultipartUpload(pathname, body, headers, options) {\n  debug(\"mpu: init\", \"pathname:\", pathname, \"headers:\", headers);\n  const optionsWithoutOnUploadProgress = {\n    ...options,\n    onUploadProgress: void 0\n  };\n  const createMultipartUploadResponse = await createMultipartUpload(\n    pathname,\n    headers,\n    optionsWithoutOnUploadProgress\n  );\n  const totalToLoad = computeBodyLength(body);\n  const stream = await toReadableStream(body);\n  const parts = await uploadAllParts({\n    uploadId: createMultipartUploadResponse.uploadId,\n    key: createMultipartUploadResponse.key,\n    pathname,\n    stream,\n    headers,\n    options,\n    totalToLoad\n  });\n  const blob = await completeMultipartUpload({\n    uploadId: createMultipartUploadResponse.uploadId,\n    key: createMultipartUploadResponse.key,\n    pathname,\n    parts,\n    headers,\n    options: optionsWithoutOnUploadProgress\n  });\n  return blob;\n}\n\n// src/put.ts\nfunction createPutMethod({\n  allowedOptions,\n  getToken,\n  extraChecks\n}) {\n  return async function put(pathname, body, optionsInput) {\n    if (!body) {\n      throw new BlobError(\"body is required\");\n    }\n    if (isPlainObject(body)) {\n      throw new BlobError(\n        \"Body must be a string, buffer or stream. You sent a plain JavaScript object, double check what you're trying to upload.\"\n      );\n    }\n    const options = await createPutOptions({\n      pathname,\n      options: optionsInput,\n      extraChecks,\n      getToken\n    });\n    const headers = createPutHeaders(allowedOptions, options);\n    if (options.multipart === true) {\n      return uncontrolledMultipartUpload(pathname, body, headers, options);\n    }\n    const onUploadProgress = options.onUploadProgress ? throttleit__WEBPACK_IMPORTED_MODULE_5__(options.onUploadProgress, 100) : void 0;\n    const params = new URLSearchParams({ pathname });\n    const response = await requestApi(\n      `/?${params.toString()}`,\n      {\n        method: \"PUT\",\n        body,\n        headers,\n        signal: options.abortSignal\n      },\n      {\n        ...options,\n        onUploadProgress\n      }\n    );\n    return {\n      url: response.url,\n      downloadUrl: response.downloadUrl,\n      pathname: response.pathname,\n      contentType: response.contentType,\n      contentDisposition: response.contentDisposition\n    };\n  };\n}\n\n// src/multipart/create-uploader.ts\nfunction createCreateMultipartUploaderMethod({ allowedOptions, getToken, extraChecks }) {\n  return async (pathname, optionsInput) => {\n    const options = await createPutOptions({\n      pathname,\n      options: optionsInput,\n      extraChecks,\n      getToken\n    });\n    const headers = createPutHeaders(allowedOptions, options);\n    const createMultipartUploadResponse = await createMultipartUpload(\n      pathname,\n      headers,\n      options\n    );\n    return {\n      key: createMultipartUploadResponse.key,\n      uploadId: createMultipartUploadResponse.uploadId,\n      async uploadPart(partNumber, body) {\n        if (isPlainObject(body)) {\n          throw new BlobError(\n            \"Body must be a string, buffer or stream. You sent a plain JavaScript object, double check what you're trying to upload.\"\n          );\n        }\n        const result = await uploadPart({\n          uploadId: createMultipartUploadResponse.uploadId,\n          key: createMultipartUploadResponse.key,\n          pathname,\n          part: { partNumber, blob: body },\n          headers,\n          options\n        });\n        return {\n          etag: result.etag,\n          partNumber\n        };\n      },\n      async complete(parts) {\n        return completeMultipartUpload({\n          uploadId: createMultipartUploadResponse.uploadId,\n          key: createMultipartUploadResponse.key,\n          pathname,\n          parts,\n          headers,\n          options\n        });\n      }\n    };\n  };\n}\n\n// src/create-folder.ts\nasync function createFolder(pathname, options = {}) {\n  const folderPathname = pathname.endsWith(\"/\") ? pathname : `${pathname}/`;\n  const headers = {};\n  headers[putOptionHeaderMap.addRandomSuffix] = \"0\";\n  const params = new URLSearchParams({ pathname: folderPathname });\n  const response = await requestApi(\n    `/?${params.toString()}`,\n    {\n      method: \"PUT\",\n      headers,\n      signal: options.abortSignal\n    },\n    options\n  );\n  return {\n    url: response.url,\n    pathname: response.pathname\n  };\n}\n\n\n/*!\n * bytes\n * Copyright(c) 2012-2014 TJ Holowaychuk\n * Copyright(c) 2015 Jed Watson\n * MIT Licensed\n */\n//# sourceMappingURL=chunk-Z56QURM6.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@vercel/blob/dist/chunk-Z56QURM6.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@vercel/blob/dist/index.js":
/*!*************************************************!*\
  !*** ./node_modules/@vercel/blob/dist/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlobAccessError: () => (/* reexport safe */ _chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.BlobAccessError),\n/* harmony export */   BlobClientTokenExpiredError: () => (/* reexport safe */ _chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.BlobClientTokenExpiredError),\n/* harmony export */   BlobContentTypeNotAllowedError: () => (/* reexport safe */ _chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.BlobContentTypeNotAllowedError),\n/* harmony export */   BlobError: () => (/* reexport safe */ _chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.BlobError),\n/* harmony export */   BlobFileTooLargeError: () => (/* reexport safe */ _chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.BlobFileTooLargeError),\n/* harmony export */   BlobNotFoundError: () => (/* reexport safe */ _chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.BlobNotFoundError),\n/* harmony export */   BlobPathnameMismatchError: () => (/* reexport safe */ _chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.BlobPathnameMismatchError),\n/* harmony export */   BlobRequestAbortedError: () => (/* reexport safe */ _chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.BlobRequestAbortedError),\n/* harmony export */   BlobServiceNotAvailable: () => (/* reexport safe */ _chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.BlobServiceNotAvailable),\n/* harmony export */   BlobServiceRateLimited: () => (/* reexport safe */ _chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.BlobServiceRateLimited),\n/* harmony export */   BlobStoreNotFoundError: () => (/* reexport safe */ _chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.BlobStoreNotFoundError),\n/* harmony export */   BlobStoreSuspendedError: () => (/* reexport safe */ _chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.BlobStoreSuspendedError),\n/* harmony export */   BlobUnknownError: () => (/* reexport safe */ _chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.BlobUnknownError),\n/* harmony export */   completeMultipartUpload: () => (/* binding */ completeMultipartUpload),\n/* harmony export */   copy: () => (/* binding */ copy),\n/* harmony export */   createFolder: () => (/* reexport safe */ _chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.createFolder),\n/* harmony export */   createMultipartUpload: () => (/* binding */ createMultipartUpload),\n/* harmony export */   createMultipartUploader: () => (/* binding */ createMultipartUploader),\n/* harmony export */   del: () => (/* binding */ del),\n/* harmony export */   getDownloadUrl: () => (/* reexport safe */ _chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.getDownloadUrl),\n/* harmony export */   head: () => (/* binding */ head),\n/* harmony export */   list: () => (/* binding */ list),\n/* harmony export */   put: () => (/* binding */ put),\n/* harmony export */   uploadPart: () => (/* binding */ uploadPart)\n/* harmony export */ });\n/* harmony import */ var _chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-Z56QURM6.js */ \"(rsc)/./node_modules/@vercel/blob/dist/chunk-Z56QURM6.js\");\n\n\n// src/del.ts\nasync function del(urlOrPathname, options) {\n  await (0,_chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.requestApi)(\n    \"/delete\",\n    {\n      method: \"POST\",\n      headers: { \"content-type\": \"application/json\" },\n      body: JSON.stringify({\n        urls: Array.isArray(urlOrPathname) ? urlOrPathname : [urlOrPathname]\n      }),\n      signal: options == null ? void 0 : options.abortSignal\n    },\n    options\n  );\n}\n\n// src/head.ts\nasync function head(urlOrPathname, options) {\n  const searchParams = new URLSearchParams({ url: urlOrPathname });\n  const response = await (0,_chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.requestApi)(\n    `?${searchParams.toString()}`,\n    // HEAD can't have body as a response, so we use GET\n    {\n      method: \"GET\",\n      signal: options == null ? void 0 : options.abortSignal\n    },\n    options\n  );\n  return {\n    url: response.url,\n    downloadUrl: response.downloadUrl,\n    pathname: response.pathname,\n    size: response.size,\n    contentType: response.contentType,\n    contentDisposition: response.contentDisposition,\n    cacheControl: response.cacheControl,\n    uploadedAt: new Date(response.uploadedAt)\n  };\n}\n\n// src/list.ts\nasync function list(options) {\n  var _a;\n  const searchParams = new URLSearchParams();\n  if (options == null ? void 0 : options.limit) {\n    searchParams.set(\"limit\", options.limit.toString());\n  }\n  if (options == null ? void 0 : options.prefix) {\n    searchParams.set(\"prefix\", options.prefix);\n  }\n  if (options == null ? void 0 : options.cursor) {\n    searchParams.set(\"cursor\", options.cursor);\n  }\n  if (options == null ? void 0 : options.mode) {\n    searchParams.set(\"mode\", options.mode);\n  }\n  const response = await (0,_chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.requestApi)(\n    `?${searchParams.toString()}`,\n    {\n      method: \"GET\",\n      signal: options == null ? void 0 : options.abortSignal\n    },\n    options\n  );\n  if ((options == null ? void 0 : options.mode) === \"folded\") {\n    return {\n      folders: (_a = response.folders) != null ? _a : [],\n      cursor: response.cursor,\n      hasMore: response.hasMore,\n      blobs: response.blobs.map(mapBlobResult)\n    };\n  }\n  return {\n    cursor: response.cursor,\n    hasMore: response.hasMore,\n    blobs: response.blobs.map(mapBlobResult)\n  };\n}\nfunction mapBlobResult(blobResult) {\n  return {\n    url: blobResult.url,\n    downloadUrl: blobResult.downloadUrl,\n    pathname: blobResult.pathname,\n    size: blobResult.size,\n    uploadedAt: new Date(blobResult.uploadedAt)\n  };\n}\n\n// src/copy.ts\nasync function copy(fromUrlOrPathname, toPathname, options) {\n  if (!options) {\n    throw new _chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.BlobError(\"missing options, see usage\");\n  }\n  if (options.access !== \"public\") {\n    throw new _chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.BlobError('access must be \"public\"');\n  }\n  if (toPathname.length > _chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.MAXIMUM_PATHNAME_LENGTH) {\n    throw new _chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.BlobError(\n      `pathname is too long, maximum length is ${_chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.MAXIMUM_PATHNAME_LENGTH}`\n    );\n  }\n  for (const invalidCharacter of _chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.disallowedPathnameCharacters) {\n    if (toPathname.includes(invalidCharacter)) {\n      throw new _chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.BlobError(\n        `pathname cannot contain \"${invalidCharacter}\", please encode it if needed`\n      );\n    }\n  }\n  const headers = {};\n  if (options.addRandomSuffix !== void 0) {\n    headers[\"x-add-random-suffix\"] = options.addRandomSuffix ? \"1\" : \"0\";\n  }\n  if (options.allowOverwrite !== void 0) {\n    headers[\"x-allow-overwrite\"] = options.allowOverwrite ? \"1\" : \"0\";\n  }\n  if (options.contentType) {\n    headers[\"x-content-type\"] = options.contentType;\n  }\n  if (options.cacheControlMaxAge !== void 0) {\n    headers[\"x-cache-control-max-age\"] = options.cacheControlMaxAge.toString();\n  }\n  const params = new URLSearchParams({\n    pathname: toPathname,\n    fromUrl: fromUrlOrPathname\n  });\n  const response = await (0,_chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.requestApi)(\n    `?${params.toString()}`,\n    {\n      method: \"PUT\",\n      headers,\n      signal: options.abortSignal\n    },\n    options\n  );\n  return {\n    url: response.url,\n    downloadUrl: response.downloadUrl,\n    pathname: response.pathname,\n    contentType: response.contentType,\n    contentDisposition: response.contentDisposition\n  };\n}\n\n// src/index.ts\nvar put = (0,_chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.createPutMethod)({\n  allowedOptions: [\n    \"cacheControlMaxAge\",\n    \"addRandomSuffix\",\n    \"allowOverwrite\",\n    \"contentType\"\n  ]\n});\nvar createMultipartUpload = (0,_chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.createCreateMultipartUploadMethod)({\n  allowedOptions: [\n    \"cacheControlMaxAge\",\n    \"addRandomSuffix\",\n    \"allowOverwrite\",\n    \"contentType\"\n  ]\n});\nvar createMultipartUploader = (0,_chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.createCreateMultipartUploaderMethod)({\n  allowedOptions: [\n    \"cacheControlMaxAge\",\n    \"addRandomSuffix\",\n    \"allowOverwrite\",\n    \"contentType\"\n  ]\n});\nvar uploadPart = (0,_chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.createUploadPartMethod)({\n  allowedOptions: [\n    \"cacheControlMaxAge\",\n    \"addRandomSuffix\",\n    \"allowOverwrite\",\n    \"contentType\"\n  ]\n});\nvar completeMultipartUpload = (0,_chunk_Z56QURM6_js__WEBPACK_IMPORTED_MODULE_0__.createCompleteMultipartUploadMethod)({\n  allowedOptions: [\n    \"cacheControlMaxAge\",\n    \"addRandomSuffix\",\n    \"allowOverwrite\",\n    \"contentType\"\n  ]\n});\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@vercel/blob/dist/index.js\n");

/***/ })

};
;