"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-commands";
exports.ids = ["vendor-chunks/prosemirror-commands"];
exports.modules = {

/***/ "(ssr)/./node_modules/prosemirror-commands/dist/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/prosemirror-commands/dist/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   autoJoin: () => (/* binding */ autoJoin),\n/* harmony export */   baseKeymap: () => (/* binding */ baseKeymap),\n/* harmony export */   chainCommands: () => (/* binding */ chainCommands),\n/* harmony export */   createParagraphNear: () => (/* binding */ createParagraphNear),\n/* harmony export */   deleteSelection: () => (/* binding */ deleteSelection),\n/* harmony export */   exitCode: () => (/* binding */ exitCode),\n/* harmony export */   joinBackward: () => (/* binding */ joinBackward),\n/* harmony export */   joinDown: () => (/* binding */ joinDown),\n/* harmony export */   joinForward: () => (/* binding */ joinForward),\n/* harmony export */   joinTextblockBackward: () => (/* binding */ joinTextblockBackward),\n/* harmony export */   joinTextblockForward: () => (/* binding */ joinTextblockForward),\n/* harmony export */   joinUp: () => (/* binding */ joinUp),\n/* harmony export */   lift: () => (/* binding */ lift),\n/* harmony export */   liftEmptyBlock: () => (/* binding */ liftEmptyBlock),\n/* harmony export */   macBaseKeymap: () => (/* binding */ macBaseKeymap),\n/* harmony export */   newlineInCode: () => (/* binding */ newlineInCode),\n/* harmony export */   pcBaseKeymap: () => (/* binding */ pcBaseKeymap),\n/* harmony export */   selectAll: () => (/* binding */ selectAll),\n/* harmony export */   selectNodeBackward: () => (/* binding */ selectNodeBackward),\n/* harmony export */   selectNodeForward: () => (/* binding */ selectNodeForward),\n/* harmony export */   selectParentNode: () => (/* binding */ selectParentNode),\n/* harmony export */   selectTextblockEnd: () => (/* binding */ selectTextblockEnd),\n/* harmony export */   selectTextblockStart: () => (/* binding */ selectTextblockStart),\n/* harmony export */   setBlockType: () => (/* binding */ setBlockType),\n/* harmony export */   splitBlock: () => (/* binding */ splitBlock),\n/* harmony export */   splitBlockAs: () => (/* binding */ splitBlockAs),\n/* harmony export */   splitBlockKeepMarks: () => (/* binding */ splitBlockKeepMarks),\n/* harmony export */   toggleMark: () => (/* binding */ toggleMark),\n/* harmony export */   wrapIn: () => (/* binding */ wrapIn)\n/* harmony export */ });\n/* harmony import */ var prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-transform */ \"(ssr)/./node_modules/prosemirror-transform/dist/index.js\");\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/./node_modules/prosemirror-model/dist/index.js\");\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/./node_modules/prosemirror-state/dist/index.js\");\n\n\n\n\n/**\nDelete the selection, if there is one.\n*/\nconst deleteSelection = (state, dispatch) => {\n    if (state.selection.empty)\n        return false;\n    if (dispatch)\n        dispatch(state.tr.deleteSelection().scrollIntoView());\n    return true;\n};\nfunction atBlockStart(state, view) {\n    let { $cursor } = state.selection;\n    if (!$cursor || (view ? !view.endOfTextblock(\"backward\", state)\n        : $cursor.parentOffset > 0))\n        return null;\n    return $cursor;\n}\n/**\nIf the selection is empty and at the start of a textblock, try to\nreduce the distance between that block and the one before it—if\nthere's a block directly before it that can be joined, join them.\nIf not, try to move the selected block closer to the next one in\nthe document structure by lifting it out of its parent or moving it\ninto a parent of the previous block. Will use the view for accurate\n(bidi-aware) start-of-textblock detection if given.\n*/\nconst joinBackward = (state, dispatch, view) => {\n    let $cursor = atBlockStart(state, view);\n    if (!$cursor)\n        return false;\n    let $cut = findCutBefore($cursor);\n    // If there is no node before this, try to lift\n    if (!$cut) {\n        let range = $cursor.blockRange(), target = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.liftTarget)(range);\n        if (target == null)\n            return false;\n        if (dispatch)\n            dispatch(state.tr.lift(range, target).scrollIntoView());\n        return true;\n    }\n    let before = $cut.nodeBefore;\n    // Apply the joining algorithm\n    if (deleteBarrier(state, $cut, dispatch, -1))\n        return true;\n    // If the node below has no content and the node above is\n    // selectable, delete the node below and select the one above.\n    if ($cursor.parent.content.size == 0 &&\n        (textblockAt(before, \"end\") || prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.isSelectable(before))) {\n        for (let depth = $cursor.depth;; depth--) {\n            let delStep = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.replaceStep)(state.doc, $cursor.before(depth), $cursor.after(depth), prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice.empty);\n            if (delStep && delStep.slice.size < delStep.to - delStep.from) {\n                if (dispatch) {\n                    let tr = state.tr.step(delStep);\n                    tr.setSelection(textblockAt(before, \"end\")\n                        ? prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.Selection.findFrom(tr.doc.resolve(tr.mapping.map($cut.pos, -1)), -1)\n                        : prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(tr.doc, $cut.pos - before.nodeSize));\n                    dispatch(tr.scrollIntoView());\n                }\n                return true;\n            }\n            if (depth == 1 || $cursor.node(depth - 1).childCount > 1)\n                break;\n        }\n    }\n    // If the node before is an atom, delete it\n    if (before.isAtom && $cut.depth == $cursor.depth - 1) {\n        if (dispatch)\n            dispatch(state.tr.delete($cut.pos - before.nodeSize, $cut.pos).scrollIntoView());\n        return true;\n    }\n    return false;\n};\n/**\nA more limited form of [`joinBackward`](https://prosemirror.net/docs/ref/#commands.joinBackward)\nthat only tries to join the current textblock to the one before\nit, if the cursor is at the start of a textblock.\n*/\nconst joinTextblockBackward = (state, dispatch, view) => {\n    let $cursor = atBlockStart(state, view);\n    if (!$cursor)\n        return false;\n    let $cut = findCutBefore($cursor);\n    return $cut ? joinTextblocksAround(state, $cut, dispatch) : false;\n};\n/**\nA more limited form of [`joinForward`](https://prosemirror.net/docs/ref/#commands.joinForward)\nthat only tries to join the current textblock to the one after\nit, if the cursor is at the end of a textblock.\n*/\nconst joinTextblockForward = (state, dispatch, view) => {\n    let $cursor = atBlockEnd(state, view);\n    if (!$cursor)\n        return false;\n    let $cut = findCutAfter($cursor);\n    return $cut ? joinTextblocksAround(state, $cut, dispatch) : false;\n};\nfunction joinTextblocksAround(state, $cut, dispatch) {\n    let before = $cut.nodeBefore, beforeText = before, beforePos = $cut.pos - 1;\n    for (; !beforeText.isTextblock; beforePos--) {\n        if (beforeText.type.spec.isolating)\n            return false;\n        let child = beforeText.lastChild;\n        if (!child)\n            return false;\n        beforeText = child;\n    }\n    let after = $cut.nodeAfter, afterText = after, afterPos = $cut.pos + 1;\n    for (; !afterText.isTextblock; afterPos++) {\n        if (afterText.type.spec.isolating)\n            return false;\n        let child = afterText.firstChild;\n        if (!child)\n            return false;\n        afterText = child;\n    }\n    let step = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.replaceStep)(state.doc, beforePos, afterPos, prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice.empty);\n    if (!step || step.from != beforePos ||\n        step instanceof prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.ReplaceStep && step.slice.size >= afterPos - beforePos)\n        return false;\n    if (dispatch) {\n        let tr = state.tr.step(step);\n        tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.TextSelection.create(tr.doc, beforePos));\n        dispatch(tr.scrollIntoView());\n    }\n    return true;\n}\nfunction textblockAt(node, side, only = false) {\n    for (let scan = node; scan; scan = (side == \"start\" ? scan.firstChild : scan.lastChild)) {\n        if (scan.isTextblock)\n            return true;\n        if (only && scan.childCount != 1)\n            return false;\n    }\n    return false;\n}\n/**\nWhen the selection is empty and at the start of a textblock, select\nthe node before that textblock, if possible. This is intended to be\nbound to keys like backspace, after\n[`joinBackward`](https://prosemirror.net/docs/ref/#commands.joinBackward) or other deleting\ncommands, as a fall-back behavior when the schema doesn't allow\ndeletion at the selected point.\n*/\nconst selectNodeBackward = (state, dispatch, view) => {\n    let { $head, empty } = state.selection, $cut = $head;\n    if (!empty)\n        return false;\n    if ($head.parent.isTextblock) {\n        if (view ? !view.endOfTextblock(\"backward\", state) : $head.parentOffset > 0)\n            return false;\n        $cut = findCutBefore($head);\n    }\n    let node = $cut && $cut.nodeBefore;\n    if (!node || !prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.isSelectable(node))\n        return false;\n    if (dispatch)\n        dispatch(state.tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(state.doc, $cut.pos - node.nodeSize)).scrollIntoView());\n    return true;\n};\nfunction findCutBefore($pos) {\n    if (!$pos.parent.type.spec.isolating)\n        for (let i = $pos.depth - 1; i >= 0; i--) {\n            if ($pos.index(i) > 0)\n                return $pos.doc.resolve($pos.before(i + 1));\n            if ($pos.node(i).type.spec.isolating)\n                break;\n        }\n    return null;\n}\nfunction atBlockEnd(state, view) {\n    let { $cursor } = state.selection;\n    if (!$cursor || (view ? !view.endOfTextblock(\"forward\", state)\n        : $cursor.parentOffset < $cursor.parent.content.size))\n        return null;\n    return $cursor;\n}\n/**\nIf the selection is empty and the cursor is at the end of a\ntextblock, try to reduce or remove the boundary between that block\nand the one after it, either by joining them or by moving the other\nblock closer to this one in the tree structure. Will use the view\nfor accurate start-of-textblock detection if given.\n*/\nconst joinForward = (state, dispatch, view) => {\n    let $cursor = atBlockEnd(state, view);\n    if (!$cursor)\n        return false;\n    let $cut = findCutAfter($cursor);\n    // If there is no node after this, there's nothing to do\n    if (!$cut)\n        return false;\n    let after = $cut.nodeAfter;\n    // Try the joining algorithm\n    if (deleteBarrier(state, $cut, dispatch, 1))\n        return true;\n    // If the node above has no content and the node below is\n    // selectable, delete the node above and select the one below.\n    if ($cursor.parent.content.size == 0 &&\n        (textblockAt(after, \"start\") || prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.isSelectable(after))) {\n        let delStep = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.replaceStep)(state.doc, $cursor.before(), $cursor.after(), prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice.empty);\n        if (delStep && delStep.slice.size < delStep.to - delStep.from) {\n            if (dispatch) {\n                let tr = state.tr.step(delStep);\n                tr.setSelection(textblockAt(after, \"start\") ? prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.Selection.findFrom(tr.doc.resolve(tr.mapping.map($cut.pos)), 1)\n                    : prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(tr.doc, tr.mapping.map($cut.pos)));\n                dispatch(tr.scrollIntoView());\n            }\n            return true;\n        }\n    }\n    // If the next node is an atom, delete it\n    if (after.isAtom && $cut.depth == $cursor.depth - 1) {\n        if (dispatch)\n            dispatch(state.tr.delete($cut.pos, $cut.pos + after.nodeSize).scrollIntoView());\n        return true;\n    }\n    return false;\n};\n/**\nWhen the selection is empty and at the end of a textblock, select\nthe node coming after that textblock, if possible. This is intended\nto be bound to keys like delete, after\n[`joinForward`](https://prosemirror.net/docs/ref/#commands.joinForward) and similar deleting\ncommands, to provide a fall-back behavior when the schema doesn't\nallow deletion at the selected point.\n*/\nconst selectNodeForward = (state, dispatch, view) => {\n    let { $head, empty } = state.selection, $cut = $head;\n    if (!empty)\n        return false;\n    if ($head.parent.isTextblock) {\n        if (view ? !view.endOfTextblock(\"forward\", state) : $head.parentOffset < $head.parent.content.size)\n            return false;\n        $cut = findCutAfter($head);\n    }\n    let node = $cut && $cut.nodeAfter;\n    if (!node || !prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.isSelectable(node))\n        return false;\n    if (dispatch)\n        dispatch(state.tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(state.doc, $cut.pos)).scrollIntoView());\n    return true;\n};\nfunction findCutAfter($pos) {\n    if (!$pos.parent.type.spec.isolating)\n        for (let i = $pos.depth - 1; i >= 0; i--) {\n            let parent = $pos.node(i);\n            if ($pos.index(i) + 1 < parent.childCount)\n                return $pos.doc.resolve($pos.after(i + 1));\n            if (parent.type.spec.isolating)\n                break;\n        }\n    return null;\n}\n/**\nJoin the selected block or, if there is a text selection, the\nclosest ancestor block of the selection that can be joined, with\nthe sibling above it.\n*/\nconst joinUp = (state, dispatch) => {\n    let sel = state.selection, nodeSel = sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection, point;\n    if (nodeSel) {\n        if (sel.node.isTextblock || !(0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(state.doc, sel.from))\n            return false;\n        point = sel.from;\n    }\n    else {\n        point = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.joinPoint)(state.doc, sel.from, -1);\n        if (point == null)\n            return false;\n    }\n    if (dispatch) {\n        let tr = state.tr.join(point);\n        if (nodeSel)\n            tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(tr.doc, point - state.doc.resolve(point).nodeBefore.nodeSize));\n        dispatch(tr.scrollIntoView());\n    }\n    return true;\n};\n/**\nJoin the selected block, or the closest ancestor of the selection\nthat can be joined, with the sibling after it.\n*/\nconst joinDown = (state, dispatch) => {\n    let sel = state.selection, point;\n    if (sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection) {\n        if (sel.node.isTextblock || !(0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(state.doc, sel.to))\n            return false;\n        point = sel.to;\n    }\n    else {\n        point = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.joinPoint)(state.doc, sel.to, 1);\n        if (point == null)\n            return false;\n    }\n    if (dispatch)\n        dispatch(state.tr.join(point).scrollIntoView());\n    return true;\n};\n/**\nLift the selected block, or the closest ancestor block of the\nselection that can be lifted, out of its parent node.\n*/\nconst lift = (state, dispatch) => {\n    let { $from, $to } = state.selection;\n    let range = $from.blockRange($to), target = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.liftTarget)(range);\n    if (target == null)\n        return false;\n    if (dispatch)\n        dispatch(state.tr.lift(range, target).scrollIntoView());\n    return true;\n};\n/**\nIf the selection is in a node whose type has a truthy\n[`code`](https://prosemirror.net/docs/ref/#model.NodeSpec.code) property in its spec, replace the\nselection with a newline character.\n*/\nconst newlineInCode = (state, dispatch) => {\n    let { $head, $anchor } = state.selection;\n    if (!$head.parent.type.spec.code || !$head.sameParent($anchor))\n        return false;\n    if (dispatch)\n        dispatch(state.tr.insertText(\"\\n\").scrollIntoView());\n    return true;\n};\nfunction defaultBlockAt(match) {\n    for (let i = 0; i < match.edgeCount; i++) {\n        let { type } = match.edge(i);\n        if (type.isTextblock && !type.hasRequiredAttrs())\n            return type;\n    }\n    return null;\n}\n/**\nWhen the selection is in a node with a truthy\n[`code`](https://prosemirror.net/docs/ref/#model.NodeSpec.code) property in its spec, create a\ndefault block after the code block, and move the cursor there.\n*/\nconst exitCode = (state, dispatch) => {\n    let { $head, $anchor } = state.selection;\n    if (!$head.parent.type.spec.code || !$head.sameParent($anchor))\n        return false;\n    let above = $head.node(-1), after = $head.indexAfter(-1), type = defaultBlockAt(above.contentMatchAt(after));\n    if (!type || !above.canReplaceWith(after, after, type))\n        return false;\n    if (dispatch) {\n        let pos = $head.after(), tr = state.tr.replaceWith(pos, pos, type.createAndFill());\n        tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.Selection.near(tr.doc.resolve(pos), 1));\n        dispatch(tr.scrollIntoView());\n    }\n    return true;\n};\n/**\nIf a block node is selected, create an empty paragraph before (if\nit is its parent's first child) or after it.\n*/\nconst createParagraphNear = (state, dispatch) => {\n    let sel = state.selection, { $from, $to } = sel;\n    if (sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.AllSelection || $from.parent.inlineContent || $to.parent.inlineContent)\n        return false;\n    let type = defaultBlockAt($to.parent.contentMatchAt($to.indexAfter()));\n    if (!type || !type.isTextblock)\n        return false;\n    if (dispatch) {\n        let side = (!$from.parentOffset && $to.index() < $to.parent.childCount ? $from : $to).pos;\n        let tr = state.tr.insert(side, type.createAndFill());\n        tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.TextSelection.create(tr.doc, side + 1));\n        dispatch(tr.scrollIntoView());\n    }\n    return true;\n};\n/**\nIf the cursor is in an empty textblock that can be lifted, lift the\nblock.\n*/\nconst liftEmptyBlock = (state, dispatch) => {\n    let { $cursor } = state.selection;\n    if (!$cursor || $cursor.parent.content.size)\n        return false;\n    if ($cursor.depth > 1 && $cursor.after() != $cursor.end(-1)) {\n        let before = $cursor.before();\n        if ((0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canSplit)(state.doc, before)) {\n            if (dispatch)\n                dispatch(state.tr.split(before).scrollIntoView());\n            return true;\n        }\n    }\n    let range = $cursor.blockRange(), target = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.liftTarget)(range);\n    if (target == null)\n        return false;\n    if (dispatch)\n        dispatch(state.tr.lift(range, target).scrollIntoView());\n    return true;\n};\n/**\nCreate a variant of [`splitBlock`](https://prosemirror.net/docs/ref/#commands.splitBlock) that uses\na custom function to determine the type of the newly split off block.\n*/\nfunction splitBlockAs(splitNode) {\n    return (state, dispatch) => {\n        let { $from, $to } = state.selection;\n        if (state.selection instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection && state.selection.node.isBlock) {\n            if (!$from.parentOffset || !(0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canSplit)(state.doc, $from.pos))\n                return false;\n            if (dispatch)\n                dispatch(state.tr.split($from.pos).scrollIntoView());\n            return true;\n        }\n        if (!$from.depth)\n            return false;\n        let types = [];\n        let splitDepth, deflt, atEnd = false, atStart = false;\n        for (let d = $from.depth;; d--) {\n            let node = $from.node(d);\n            if (node.isBlock) {\n                atEnd = $from.end(d) == $from.pos + ($from.depth - d);\n                atStart = $from.start(d) == $from.pos - ($from.depth - d);\n                deflt = defaultBlockAt($from.node(d - 1).contentMatchAt($from.indexAfter(d - 1)));\n                let splitType = splitNode && splitNode($to.parent, atEnd, $from);\n                types.unshift(splitType || (atEnd && deflt ? { type: deflt } : null));\n                splitDepth = d;\n                break;\n            }\n            else {\n                if (d == 1)\n                    return false;\n                types.unshift(null);\n            }\n        }\n        let tr = state.tr;\n        if (state.selection instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.TextSelection || state.selection instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.AllSelection)\n            tr.deleteSelection();\n        let splitPos = tr.mapping.map($from.pos);\n        let can = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canSplit)(tr.doc, splitPos, types.length, types);\n        if (!can) {\n            types[0] = deflt ? { type: deflt } : null;\n            can = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canSplit)(tr.doc, splitPos, types.length, types);\n        }\n        if (!can)\n            return false;\n        tr.split(splitPos, types.length, types);\n        if (!atEnd && atStart && $from.node(splitDepth).type != deflt) {\n            let first = tr.mapping.map($from.before(splitDepth)), $first = tr.doc.resolve(first);\n            if (deflt && $from.node(splitDepth - 1).canReplaceWith($first.index(), $first.index() + 1, deflt))\n                tr.setNodeMarkup(tr.mapping.map($from.before(splitDepth)), deflt);\n        }\n        if (dispatch)\n            dispatch(tr.scrollIntoView());\n        return true;\n    };\n}\n/**\nSplit the parent block of the selection. If the selection is a text\nselection, also delete its content.\n*/\nconst splitBlock = splitBlockAs();\n/**\nActs like [`splitBlock`](https://prosemirror.net/docs/ref/#commands.splitBlock), but without\nresetting the set of active marks at the cursor.\n*/\nconst splitBlockKeepMarks = (state, dispatch) => {\n    return splitBlock(state, dispatch && (tr => {\n        let marks = state.storedMarks || (state.selection.$to.parentOffset && state.selection.$from.marks());\n        if (marks)\n            tr.ensureMarks(marks);\n        dispatch(tr);\n    }));\n};\n/**\nMove the selection to the node wrapping the current selection, if\nany. (Will not select the document node.)\n*/\nconst selectParentNode = (state, dispatch) => {\n    let { $from, to } = state.selection, pos;\n    let same = $from.sharedDepth(to);\n    if (same == 0)\n        return false;\n    pos = $from.before(same);\n    if (dispatch)\n        dispatch(state.tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(state.doc, pos)));\n    return true;\n};\n/**\nSelect the whole document.\n*/\nconst selectAll = (state, dispatch) => {\n    if (dispatch)\n        dispatch(state.tr.setSelection(new prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.AllSelection(state.doc)));\n    return true;\n};\nfunction joinMaybeClear(state, $pos, dispatch) {\n    let before = $pos.nodeBefore, after = $pos.nodeAfter, index = $pos.index();\n    if (!before || !after || !before.type.compatibleContent(after.type))\n        return false;\n    if (!before.content.size && $pos.parent.canReplace(index - 1, index)) {\n        if (dispatch)\n            dispatch(state.tr.delete($pos.pos - before.nodeSize, $pos.pos).scrollIntoView());\n        return true;\n    }\n    if (!$pos.parent.canReplace(index, index + 1) || !(after.isTextblock || (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(state.doc, $pos.pos)))\n        return false;\n    if (dispatch)\n        dispatch(state.tr.join($pos.pos).scrollIntoView());\n    return true;\n}\nfunction deleteBarrier(state, $cut, dispatch, dir) {\n    let before = $cut.nodeBefore, after = $cut.nodeAfter, conn, match;\n    let isolated = before.type.spec.isolating || after.type.spec.isolating;\n    if (!isolated && joinMaybeClear(state, $cut, dispatch))\n        return true;\n    let canDelAfter = !isolated && $cut.parent.canReplace($cut.index(), $cut.index() + 1);\n    if (canDelAfter &&\n        (conn = (match = before.contentMatchAt(before.childCount)).findWrapping(after.type)) &&\n        match.matchType(conn[0] || after.type).validEnd) {\n        if (dispatch) {\n            let end = $cut.pos + after.nodeSize, wrap = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.empty;\n            for (let i = conn.length - 1; i >= 0; i--)\n                wrap = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.from(conn[i].create(null, wrap));\n            wrap = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.from(before.copy(wrap));\n            let tr = state.tr.step(new prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.ReplaceAroundStep($cut.pos - 1, end, $cut.pos, end, new prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice(wrap, 1, 0), conn.length, true));\n            let $joinAt = tr.doc.resolve(end + 2 * conn.length);\n            if ($joinAt.nodeAfter && $joinAt.nodeAfter.type == before.type &&\n                (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(tr.doc, $joinAt.pos))\n                tr.join($joinAt.pos);\n            dispatch(tr.scrollIntoView());\n        }\n        return true;\n    }\n    let selAfter = after.type.spec.isolating || (dir > 0 && isolated) ? null : prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.Selection.findFrom($cut, 1);\n    let range = selAfter && selAfter.$from.blockRange(selAfter.$to), target = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.liftTarget)(range);\n    if (target != null && target >= $cut.depth) {\n        if (dispatch)\n            dispatch(state.tr.lift(range, target).scrollIntoView());\n        return true;\n    }\n    if (canDelAfter && textblockAt(after, \"start\", true) && textblockAt(before, \"end\")) {\n        let at = before, wrap = [];\n        for (;;) {\n            wrap.push(at);\n            if (at.isTextblock)\n                break;\n            at = at.lastChild;\n        }\n        let afterText = after, afterDepth = 1;\n        for (; !afterText.isTextblock; afterText = afterText.firstChild)\n            afterDepth++;\n        if (at.canReplace(at.childCount, at.childCount, afterText.content)) {\n            if (dispatch) {\n                let end = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.empty;\n                for (let i = wrap.length - 1; i >= 0; i--)\n                    end = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.from(wrap[i].copy(end));\n                let tr = state.tr.step(new prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.ReplaceAroundStep($cut.pos - wrap.length, $cut.pos + after.nodeSize, $cut.pos + afterDepth, $cut.pos + after.nodeSize - afterDepth, new prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice(end, wrap.length, 0), 0, true));\n                dispatch(tr.scrollIntoView());\n            }\n            return true;\n        }\n    }\n    return false;\n}\nfunction selectTextblockSide(side) {\n    return function (state, dispatch) {\n        let sel = state.selection, $pos = side < 0 ? sel.$from : sel.$to;\n        let depth = $pos.depth;\n        while ($pos.node(depth).isInline) {\n            if (!depth)\n                return false;\n            depth--;\n        }\n        if (!$pos.node(depth).isTextblock)\n            return false;\n        if (dispatch)\n            dispatch(state.tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.TextSelection.create(state.doc, side < 0 ? $pos.start(depth) : $pos.end(depth))));\n        return true;\n    };\n}\n/**\nMoves the cursor to the start of current text block.\n*/\nconst selectTextblockStart = selectTextblockSide(-1);\n/**\nMoves the cursor to the end of current text block.\n*/\nconst selectTextblockEnd = selectTextblockSide(1);\n// Parameterized commands\n/**\nWrap the selection in a node of the given type with the given\nattributes.\n*/\nfunction wrapIn(nodeType, attrs = null) {\n    return function (state, dispatch) {\n        let { $from, $to } = state.selection;\n        let range = $from.blockRange($to), wrapping = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.findWrapping)(range, nodeType, attrs);\n        if (!wrapping)\n            return false;\n        if (dispatch)\n            dispatch(state.tr.wrap(range, wrapping).scrollIntoView());\n        return true;\n    };\n}\n/**\nReturns a command that tries to set the selected textblocks to the\ngiven node type with the given attributes.\n*/\nfunction setBlockType(nodeType, attrs = null) {\n    return function (state, dispatch) {\n        let applicable = false;\n        for (let i = 0; i < state.selection.ranges.length && !applicable; i++) {\n            let { $from: { pos: from }, $to: { pos: to } } = state.selection.ranges[i];\n            state.doc.nodesBetween(from, to, (node, pos) => {\n                if (applicable)\n                    return false;\n                if (!node.isTextblock || node.hasMarkup(nodeType, attrs))\n                    return;\n                if (node.type == nodeType) {\n                    applicable = true;\n                }\n                else {\n                    let $pos = state.doc.resolve(pos), index = $pos.index();\n                    applicable = $pos.parent.canReplaceWith(index, index + 1, nodeType);\n                }\n            });\n        }\n        if (!applicable)\n            return false;\n        if (dispatch) {\n            let tr = state.tr;\n            for (let i = 0; i < state.selection.ranges.length; i++) {\n                let { $from: { pos: from }, $to: { pos: to } } = state.selection.ranges[i];\n                tr.setBlockType(from, to, nodeType, attrs);\n            }\n            dispatch(tr.scrollIntoView());\n        }\n        return true;\n    };\n}\nfunction markApplies(doc, ranges, type, enterAtoms) {\n    for (let i = 0; i < ranges.length; i++) {\n        let { $from, $to } = ranges[i];\n        let can = $from.depth == 0 ? doc.inlineContent && doc.type.allowsMarkType(type) : false;\n        doc.nodesBetween($from.pos, $to.pos, (node, pos) => {\n            if (can || !enterAtoms && node.isAtom && node.isInline && pos >= $from.pos && pos + node.nodeSize <= $to.pos)\n                return false;\n            can = node.inlineContent && node.type.allowsMarkType(type);\n        });\n        if (can)\n            return true;\n    }\n    return false;\n}\nfunction removeInlineAtoms(ranges) {\n    let result = [];\n    for (let i = 0; i < ranges.length; i++) {\n        let { $from, $to } = ranges[i];\n        $from.doc.nodesBetween($from.pos, $to.pos, (node, pos) => {\n            if (node.isAtom && node.content.size && node.isInline && pos >= $from.pos && pos + node.nodeSize <= $to.pos) {\n                if (pos + 1 > $from.pos)\n                    result.push(new prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.SelectionRange($from, $from.doc.resolve(pos + 1)));\n                $from = $from.doc.resolve(pos + 1 + node.content.size);\n                return false;\n            }\n        });\n        if ($from.pos < $to.pos)\n            result.push(new prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.SelectionRange($from, $to));\n    }\n    return result;\n}\n/**\nCreate a command function that toggles the given mark with the\ngiven attributes. Will return `false` when the current selection\ndoesn't support that mark. This will remove the mark if any marks\nof that type exist in the selection, or add it otherwise. If the\nselection is empty, this applies to the [stored\nmarks](https://prosemirror.net/docs/ref/#state.EditorState.storedMarks) instead of a range of the\ndocument.\n*/\nfunction toggleMark(markType, attrs = null, options) {\n    let removeWhenPresent = (options && options.removeWhenPresent) !== false;\n    let enterAtoms = (options && options.enterInlineAtoms) !== false;\n    let dropSpace = !(options && options.includeWhitespace);\n    return function (state, dispatch) {\n        let { empty, $cursor, ranges } = state.selection;\n        if ((empty && !$cursor) || !markApplies(state.doc, ranges, markType, enterAtoms))\n            return false;\n        if (dispatch) {\n            if ($cursor) {\n                if (markType.isInSet(state.storedMarks || $cursor.marks()))\n                    dispatch(state.tr.removeStoredMark(markType));\n                else\n                    dispatch(state.tr.addStoredMark(markType.create(attrs)));\n            }\n            else {\n                let add, tr = state.tr;\n                if (!enterAtoms)\n                    ranges = removeInlineAtoms(ranges);\n                if (removeWhenPresent) {\n                    add = !ranges.some(r => state.doc.rangeHasMark(r.$from.pos, r.$to.pos, markType));\n                }\n                else {\n                    add = !ranges.every(r => {\n                        let missing = false;\n                        tr.doc.nodesBetween(r.$from.pos, r.$to.pos, (node, pos, parent) => {\n                            if (missing)\n                                return false;\n                            missing = !markType.isInSet(node.marks) && !!parent && parent.type.allowsMarkType(markType) &&\n                                !(node.isText && /^\\s*$/.test(node.textBetween(Math.max(0, r.$from.pos - pos), Math.min(node.nodeSize, r.$to.pos - pos))));\n                        });\n                        return !missing;\n                    });\n                }\n                for (let i = 0; i < ranges.length; i++) {\n                    let { $from, $to } = ranges[i];\n                    if (!add) {\n                        tr.removeMark($from.pos, $to.pos, markType);\n                    }\n                    else {\n                        let from = $from.pos, to = $to.pos, start = $from.nodeAfter, end = $to.nodeBefore;\n                        let spaceStart = dropSpace && start && start.isText ? /^\\s*/.exec(start.text)[0].length : 0;\n                        let spaceEnd = dropSpace && end && end.isText ? /\\s*$/.exec(end.text)[0].length : 0;\n                        if (from + spaceStart < to) {\n                            from += spaceStart;\n                            to -= spaceEnd;\n                        }\n                        tr.addMark(from, to, markType.create(attrs));\n                    }\n                }\n                dispatch(tr.scrollIntoView());\n            }\n        }\n        return true;\n    };\n}\nfunction wrapDispatchForJoin(dispatch, isJoinable) {\n    return (tr) => {\n        if (!tr.isGeneric)\n            return dispatch(tr);\n        let ranges = [];\n        for (let i = 0; i < tr.mapping.maps.length; i++) {\n            let map = tr.mapping.maps[i];\n            for (let j = 0; j < ranges.length; j++)\n                ranges[j] = map.map(ranges[j]);\n            map.forEach((_s, _e, from, to) => ranges.push(from, to));\n        }\n        // Figure out which joinable points exist inside those ranges,\n        // by checking all node boundaries in their parent nodes.\n        let joinable = [];\n        for (let i = 0; i < ranges.length; i += 2) {\n            let from = ranges[i], to = ranges[i + 1];\n            let $from = tr.doc.resolve(from), depth = $from.sharedDepth(to), parent = $from.node(depth);\n            for (let index = $from.indexAfter(depth), pos = $from.after(depth + 1); pos <= to; ++index) {\n                let after = parent.maybeChild(index);\n                if (!after)\n                    break;\n                if (index && joinable.indexOf(pos) == -1) {\n                    let before = parent.child(index - 1);\n                    if (before.type == after.type && isJoinable(before, after))\n                        joinable.push(pos);\n                }\n                pos += after.nodeSize;\n            }\n        }\n        // Join the joinable points\n        joinable.sort((a, b) => a - b);\n        for (let i = joinable.length - 1; i >= 0; i--) {\n            if ((0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(tr.doc, joinable[i]))\n                tr.join(joinable[i]);\n        }\n        dispatch(tr);\n    };\n}\n/**\nWrap a command so that, when it produces a transform that causes\ntwo joinable nodes to end up next to each other, those are joined.\nNodes are considered joinable when they are of the same type and\nwhen the `isJoinable` predicate returns true for them or, if an\narray of strings was passed, if their node type name is in that\narray.\n*/\nfunction autoJoin(command, isJoinable) {\n    let canJoin = Array.isArray(isJoinable) ? (node) => isJoinable.indexOf(node.type.name) > -1\n        : isJoinable;\n    return (state, dispatch, view) => command(state, dispatch && wrapDispatchForJoin(dispatch, canJoin), view);\n}\n/**\nCombine a number of command functions into a single function (which\ncalls them one by one until one returns true).\n*/\nfunction chainCommands(...commands) {\n    return function (state, dispatch, view) {\n        for (let i = 0; i < commands.length; i++)\n            if (commands[i](state, dispatch, view))\n                return true;\n        return false;\n    };\n}\nlet backspace = chainCommands(deleteSelection, joinBackward, selectNodeBackward);\nlet del = chainCommands(deleteSelection, joinForward, selectNodeForward);\n/**\nA basic keymap containing bindings not specific to any schema.\nBinds the following keys (when multiple commands are listed, they\nare chained with [`chainCommands`](https://prosemirror.net/docs/ref/#commands.chainCommands)):\n\n* **Enter** to `newlineInCode`, `createParagraphNear`, `liftEmptyBlock`, `splitBlock`\n* **Mod-Enter** to `exitCode`\n* **Backspace** and **Mod-Backspace** to `deleteSelection`, `joinBackward`, `selectNodeBackward`\n* **Delete** and **Mod-Delete** to `deleteSelection`, `joinForward`, `selectNodeForward`\n* **Mod-Delete** to `deleteSelection`, `joinForward`, `selectNodeForward`\n* **Mod-a** to `selectAll`\n*/\nconst pcBaseKeymap = {\n    \"Enter\": chainCommands(newlineInCode, createParagraphNear, liftEmptyBlock, splitBlock),\n    \"Mod-Enter\": exitCode,\n    \"Backspace\": backspace,\n    \"Mod-Backspace\": backspace,\n    \"Shift-Backspace\": backspace,\n    \"Delete\": del,\n    \"Mod-Delete\": del,\n    \"Mod-a\": selectAll\n};\n/**\nA copy of `pcBaseKeymap` that also binds **Ctrl-h** like Backspace,\n**Ctrl-d** like Delete, **Alt-Backspace** like Ctrl-Backspace, and\n**Ctrl-Alt-Backspace**, **Alt-Delete**, and **Alt-d** like\nCtrl-Delete.\n*/\nconst macBaseKeymap = {\n    \"Ctrl-h\": pcBaseKeymap[\"Backspace\"],\n    \"Alt-Backspace\": pcBaseKeymap[\"Mod-Backspace\"],\n    \"Ctrl-d\": pcBaseKeymap[\"Delete\"],\n    \"Ctrl-Alt-Backspace\": pcBaseKeymap[\"Mod-Delete\"],\n    \"Alt-Delete\": pcBaseKeymap[\"Mod-Delete\"],\n    \"Alt-d\": pcBaseKeymap[\"Mod-Delete\"],\n    \"Ctrl-a\": selectTextblockStart,\n    \"Ctrl-e\": selectTextblockEnd\n};\nfor (let key in pcBaseKeymap)\n    macBaseKeymap[key] = pcBaseKeymap[key];\nconst mac = typeof navigator != \"undefined\" ? /Mac|iP(hone|[oa]d)/.test(navigator.platform)\n    // @ts-ignore\n    : typeof os != \"undefined\" && os.platform ? os.platform() == \"darwin\" : false;\n/**\nDepending on the detected platform, this will hold\n[`pcBasekeymap`](https://prosemirror.net/docs/ref/#commands.pcBaseKeymap) or\n[`macBaseKeymap`](https://prosemirror.net/docs/ref/#commands.macBaseKeymap).\n*/\nconst baseKeymap = mac ? macBaseKeymap : pcBaseKeymap;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prosemirror-commands/dist/index.js\n");

/***/ })

};
;