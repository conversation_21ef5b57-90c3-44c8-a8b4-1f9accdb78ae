{"/api/auth/notifications/route": "app/api/auth/notifications/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/page": "app/page.js", "/books/page": "app/books/page.js", "/repository/[id]/page": "app/repository/[id]/page.js", "/manuscripts/[id]/page": "app/manuscripts/[id]/page.js", "/repository/page": "app/repository/page.js", "/dashboard/page": "app/dashboard/page.js"}