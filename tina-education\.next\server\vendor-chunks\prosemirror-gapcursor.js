"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-gapcursor";
exports.ids = ["vendor-chunks/prosemirror-gapcursor"];
exports.modules = {

/***/ "(ssr)/./node_modules/prosemirror-gapcursor/dist/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/prosemirror-gapcursor/dist/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GapCursor: () => (/* binding */ GapCursor),\n/* harmony export */   gapCursor: () => (/* binding */ gapCursor)\n/* harmony export */ });\n/* harmony import */ var prosemirror_keymap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prosemirror-keymap */ \"(ssr)/./node_modules/prosemirror-keymap/dist/index.js\");\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/./node_modules/prosemirror-state/dist/index.js\");\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/./node_modules/prosemirror-model/dist/index.js\");\n/* harmony import */ var prosemirror_view__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! prosemirror-view */ \"(ssr)/./node_modules/prosemirror-view/dist/index.js\");\n\n\n\n\n\n/**\nGap cursor selections are represented using this class. Its\n`$anchor` and `$head` properties both point at the cursor position.\n*/\nclass GapCursor extends prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection {\n    /**\n    Create a gap cursor.\n    */\n    constructor($pos) {\n        super($pos, $pos);\n    }\n    map(doc, mapping) {\n        let $pos = doc.resolve(mapping.map(this.head));\n        return GapCursor.valid($pos) ? new GapCursor($pos) : prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.near($pos);\n    }\n    content() { return prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice.empty; }\n    eq(other) {\n        return other instanceof GapCursor && other.head == this.head;\n    }\n    toJSON() {\n        return { type: \"gapcursor\", pos: this.head };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(doc, json) {\n        if (typeof json.pos != \"number\")\n            throw new RangeError(\"Invalid input for GapCursor.fromJSON\");\n        return new GapCursor(doc.resolve(json.pos));\n    }\n    /**\n    @internal\n    */\n    getBookmark() { return new GapBookmark(this.anchor); }\n    /**\n    @internal\n    */\n    static valid($pos) {\n        let parent = $pos.parent;\n        if (parent.isTextblock || !closedBefore($pos) || !closedAfter($pos))\n            return false;\n        let override = parent.type.spec.allowGapCursor;\n        if (override != null)\n            return override;\n        let deflt = parent.contentMatchAt($pos.index()).defaultType;\n        return deflt && deflt.isTextblock;\n    }\n    /**\n    @internal\n    */\n    static findGapCursorFrom($pos, dir, mustMove = false) {\n        search: for (;;) {\n            if (!mustMove && GapCursor.valid($pos))\n                return $pos;\n            let pos = $pos.pos, next = null;\n            // Scan up from this position\n            for (let d = $pos.depth;; d--) {\n                let parent = $pos.node(d);\n                if (dir > 0 ? $pos.indexAfter(d) < parent.childCount : $pos.index(d) > 0) {\n                    next = parent.child(dir > 0 ? $pos.indexAfter(d) : $pos.index(d) - 1);\n                    break;\n                }\n                else if (d == 0) {\n                    return null;\n                }\n                pos += dir;\n                let $cur = $pos.doc.resolve(pos);\n                if (GapCursor.valid($cur))\n                    return $cur;\n            }\n            // And then down into the next node\n            for (;;) {\n                let inside = dir > 0 ? next.firstChild : next.lastChild;\n                if (!inside) {\n                    if (next.isAtom && !next.isText && !prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.NodeSelection.isSelectable(next)) {\n                        $pos = $pos.doc.resolve(pos + next.nodeSize * dir);\n                        mustMove = false;\n                        continue search;\n                    }\n                    break;\n                }\n                next = inside;\n                pos += dir;\n                let $cur = $pos.doc.resolve(pos);\n                if (GapCursor.valid($cur))\n                    return $cur;\n            }\n            return null;\n        }\n    }\n}\nGapCursor.prototype.visible = false;\nGapCursor.findFrom = GapCursor.findGapCursorFrom;\nprosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.jsonID(\"gapcursor\", GapCursor);\nclass GapBookmark {\n    constructor(pos) {\n        this.pos = pos;\n    }\n    map(mapping) {\n        return new GapBookmark(mapping.map(this.pos));\n    }\n    resolve(doc) {\n        let $pos = doc.resolve(this.pos);\n        return GapCursor.valid($pos) ? new GapCursor($pos) : prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.near($pos);\n    }\n}\nfunction closedBefore($pos) {\n    for (let d = $pos.depth; d >= 0; d--) {\n        let index = $pos.index(d), parent = $pos.node(d);\n        // At the start of this parent, look at next one\n        if (index == 0) {\n            if (parent.type.spec.isolating)\n                return true;\n            continue;\n        }\n        // See if the node before (or its first ancestor) is closed\n        for (let before = parent.child(index - 1);; before = before.lastChild) {\n            if ((before.childCount == 0 && !before.inlineContent) || before.isAtom || before.type.spec.isolating)\n                return true;\n            if (before.inlineContent)\n                return false;\n        }\n    }\n    // Hit start of document\n    return true;\n}\nfunction closedAfter($pos) {\n    for (let d = $pos.depth; d >= 0; d--) {\n        let index = $pos.indexAfter(d), parent = $pos.node(d);\n        if (index == parent.childCount) {\n            if (parent.type.spec.isolating)\n                return true;\n            continue;\n        }\n        for (let after = parent.child(index);; after = after.firstChild) {\n            if ((after.childCount == 0 && !after.inlineContent) || after.isAtom || after.type.spec.isolating)\n                return true;\n            if (after.inlineContent)\n                return false;\n        }\n    }\n    return true;\n}\n\n/**\nCreate a gap cursor plugin. When enabled, this will capture clicks\nnear and arrow-key-motion past places that don't have a normally\nselectable position nearby, and create a gap cursor selection for\nthem. The cursor is drawn as an element with class\n`ProseMirror-gapcursor`. You can either include\n`style/gapcursor.css` from the package's directory or add your own\nstyles to make it visible.\n*/\nfunction gapCursor() {\n    return new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n        props: {\n            decorations: drawGapCursor,\n            createSelectionBetween(_view, $anchor, $head) {\n                return $anchor.pos == $head.pos && GapCursor.valid($head) ? new GapCursor($head) : null;\n            },\n            handleClick,\n            handleKeyDown,\n            handleDOMEvents: { beforeinput: beforeinput }\n        }\n    });\n}\nconst handleKeyDown = (0,prosemirror_keymap__WEBPACK_IMPORTED_MODULE_2__.keydownHandler)({\n    \"ArrowLeft\": arrow(\"horiz\", -1),\n    \"ArrowRight\": arrow(\"horiz\", 1),\n    \"ArrowUp\": arrow(\"vert\", -1),\n    \"ArrowDown\": arrow(\"vert\", 1)\n});\nfunction arrow(axis, dir) {\n    const dirStr = axis == \"vert\" ? (dir > 0 ? \"down\" : \"up\") : (dir > 0 ? \"right\" : \"left\");\n    return function (state, dispatch, view) {\n        let sel = state.selection;\n        let $start = dir > 0 ? sel.$to : sel.$from, mustMove = sel.empty;\n        if (sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection) {\n            if (!view.endOfTextblock(dirStr) || $start.depth == 0)\n                return false;\n            mustMove = false;\n            $start = state.doc.resolve(dir > 0 ? $start.after() : $start.before());\n        }\n        let $found = GapCursor.findGapCursorFrom($start, dir, mustMove);\n        if (!$found)\n            return false;\n        if (dispatch)\n            dispatch(state.tr.setSelection(new GapCursor($found)));\n        return true;\n    };\n}\nfunction handleClick(view, pos, event) {\n    if (!view || !view.editable)\n        return false;\n    let $pos = view.state.doc.resolve(pos);\n    if (!GapCursor.valid($pos))\n        return false;\n    let clickPos = view.posAtCoords({ left: event.clientX, top: event.clientY });\n    if (clickPos && clickPos.inside > -1 && prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.NodeSelection.isSelectable(view.state.doc.nodeAt(clickPos.inside)))\n        return false;\n    view.dispatch(view.state.tr.setSelection(new GapCursor($pos)));\n    return true;\n}\n// This is a hack that, when a composition starts while a gap cursor\n// is active, quickly creates an inline context for the composition to\n// happen in, to avoid it being aborted by the DOM selection being\n// moved into a valid position.\nfunction beforeinput(view, event) {\n    if (event.inputType != \"insertCompositionText\" || !(view.state.selection instanceof GapCursor))\n        return false;\n    let { $from } = view.state.selection;\n    let insert = $from.parent.contentMatchAt($from.index()).findWrapping(view.state.schema.nodes.text);\n    if (!insert)\n        return false;\n    let frag = prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.empty;\n    for (let i = insert.length - 1; i >= 0; i--)\n        frag = prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(insert[i].createAndFill(null, frag));\n    let tr = view.state.tr.replace($from.pos, $from.pos, new prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice(frag, 0, 0));\n    tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection.near(tr.doc.resolve($from.pos + 1)));\n    view.dispatch(tr);\n    return false;\n}\nfunction drawGapCursor(state) {\n    if (!(state.selection instanceof GapCursor))\n        return null;\n    let node = document.createElement(\"div\");\n    node.className = \"ProseMirror-gapcursor\";\n    return prosemirror_view__WEBPACK_IMPORTED_MODULE_3__.DecorationSet.create(state.doc, [prosemirror_view__WEBPACK_IMPORTED_MODULE_3__.Decoration.widget(state.selection.head, node, { key: \"gapcursor\" })]);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prosemirror-gapcursor/dist/index.js\n");

/***/ })

};
;